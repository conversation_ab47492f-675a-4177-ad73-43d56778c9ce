"""
Enhanced logging configuration with structured logging and performance metrics.
"""
import logging
import logging.config
import time
import functools
from typing import Any, Callable
import json
import os
from datetime import datetime

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'execution_time'):
            log_entry['execution_time'] = record.execution_time
        if hasattr(record, 'error_type'):
            log_entry['error_type'] = record.error_type
            
        return json.dumps(log_entry)

def setup_logging():
    """Setup enhanced logging configuration."""
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    log_format = os.environ.get('LOG_FORMAT', 'structured')  # 'structured' or 'simple'
    
    if log_format == 'structured':
        formatter = StructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # File handler for errors
    error_handler = logging.FileHandler('logs/error.log')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    
    # File handler for all logs
    info_handler = logging.FileHandler('logs/app.log')
    info_handler.setFormatter(formatter)
    
    # Root logger configuration
    logging.basicConfig(
        level=getattr(logging, log_level),
        handlers=[console_handler, error_handler, info_handler],
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Set specific logger levels
    logging.getLogger('azure').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

def log_performance(func_name: str = None):
    """Decorator to log function performance metrics."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            logger = logging.getLogger(func.__module__)
            function_name = func_name or func.__name__
            
            try:
                logger.info(f"Starting {function_name}")
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.info(
                    f"Completed {function_name}",
                    extra={'execution_time': execution_time}
                )
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Failed {function_name}: {str(e)}",
                    extra={
                        'execution_time': execution_time,
                        'error_type': type(e).__name__
                    }
                )
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            logger = logging.getLogger(func.__module__)
            function_name = func_name or func.__name__
            
            try:
                logger.info(f"Starting {function_name}")
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.info(
                    f"Completed {function_name}",
                    extra={'execution_time': execution_time}
                )
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"Failed {function_name}: {str(e)}",
                    extra={
                        'execution_time': execution_time,
                        'error_type': type(e).__name__
                    }
                )
                raise
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class PerformanceMetrics:
    """Simple in-memory performance metrics collector."""
    
    def __init__(self):
        self.metrics = {
            'request_count': 0,
            'total_processing_time': 0,
            'error_count': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'model_load_count': 0,
            'db_query_count': 0
        }
        self._lock = asyncio.Lock() if 'asyncio' in globals() else None
    
    async def increment(self, metric: str, value: float = 1):
        """Increment a metric value."""
        if self._lock:
            async with self._lock:
                self.metrics[metric] = self.metrics.get(metric, 0) + value
        else:
            self.metrics[metric] = self.metrics.get(metric, 0) + value
    
    def get_metrics(self) -> dict:
        """Get current metrics snapshot."""
        return self.metrics.copy()
    
    def reset(self):
        """Reset all metrics."""
        for key in self.metrics:
            self.metrics[key] = 0

# Global metrics instance
performance_metrics = PerformanceMetrics()

# Import asyncio after class definition
import asyncio
