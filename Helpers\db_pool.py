"""
Database connection pooling for improved performance and resource management.
"""
import aioodbc
import asyncio
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
import time
import os

logger = logging.getLogger(__name__)

class DatabasePool:
    """Async database connection pool with health monitoring and retry logic."""
    
    def __init__(self, connection_string: str, min_size: int = 5, max_size: int = 20):
        self.connection_string = connection_string
        self.min_size = min_size
        self.max_size = max_size
        self._pool: Optional[aioodbc.Pool] = None
        self._lock = asyncio.Lock()
        self._stats = {
            'connections_created': 0,
            'queries_executed': 0,
            'errors': 0,
            'avg_query_time': 0.0,
            'total_query_time': 0.0
        }
        self._retry_attempts = 3
        self._retry_delay = 1.0
    
    async def initialize(self):
        """Initialize the connection pool with retry logic."""
        async with self._lock:
            if self._pool is None:
                for attempt in range(self._retry_attempts):
                    try:
                        self._pool = await aioodbc.create_pool(
                            dsn=self.connection_string,
                            minsize=self.min_size,
                            maxsize=self.max_size,
                            echo=False,
                            timeout=30,
                            pool_recycle=3600  # Recycle connections every hour
                        )
                        self._stats['connections_created'] = self.min_size
                        logger.info(f"Database pool initialized with {self.min_size}-{self.max_size} connections")
                        return
                    except Exception as e:
                        logger.error(f"Failed to initialize database pool (attempt {attempt + 1}): {e}")
                        if attempt < self._retry_attempts - 1:
                            await asyncio.sleep(self._retry_delay * (2 ** attempt))  # Exponential backoff
                        else:
                            raise
    
    async def close(self):
        """Close the connection pool."""
        if self._pool:
            self._pool.close()
            await self._pool.wait_closed()
            self._pool = None
            logger.info("Database pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get a connection from the pool with automatic retry."""
        if self._pool is None:
            await self.initialize()
        
        for attempt in range(self._retry_attempts):
            try:
                async with self._pool.acquire() as conn:
                    yield conn
                    return
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"Database connection failed (attempt {attempt + 1}): {e}")
                if attempt < self._retry_attempts - 1:
                    await asyncio.sleep(self._retry_delay)
                else:
                    raise
    
    async def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute a query and return results as a list of dictionaries."""
        start_time = time.time()
        
        try:
            async with self.get_connection() as conn:
                async with conn.cursor() as cursor:
                    if params:
                        await cursor.execute(query, params)
                    else:
                        await cursor.execute(query)
                    
                    if cursor.description:
                        rows = await cursor.fetchall()
                        columns = [desc[0] for desc in cursor.description]
                        result = [dict(zip(columns, row)) for row in rows]
                    else:
                        result = []
                    
                    # Update stats
                    query_time = time.time() - start_time
                    self._stats['queries_executed'] += 1
                    self._stats['total_query_time'] += query_time
                    self._stats['avg_query_time'] = (
                        self._stats['total_query_time'] / self._stats['queries_executed']
                    )
                    
                    logger.debug(f"Query executed in {query_time:.3f}s")
                    return result
                    
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"Query execution failed: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Check if the database connection is healthy."""
        try:
            await self.execute_query("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Get connection pool statistics."""
        pool_stats = {}
        if self._pool:
            pool_stats = {
                'size': self._pool.size,
                'checked_in': self._pool.checked_in(),
                'checked_out': self._pool.checked_out(),
                'overflow': self._pool.overflow(),
                'invalid': self._pool.invalid()
            }
        
        return {
            **self._stats,
            'pool': pool_stats,
            'min_size': self.min_size,
            'max_size': self.max_size
        }

# Global pool instance
_db_pool: Optional[DatabasePool] = None

async def get_db_pool(connection_string: str) -> DatabasePool:
    """Get or create the global database pool."""
    global _db_pool
    if _db_pool is None:
        _db_pool = DatabasePool(
            connection_string,
            min_size=int(os.environ.get('DB_POOL_MIN_SIZE', 5)),
            max_size=int(os.environ.get('DB_POOL_MAX_SIZE', 20))
        )
        await _db_pool.initialize()
    return _db_pool

async def close_db_pool():
    """Close the global database pool."""
    global _db_pool
    if _db_pool:
        await _db_pool.close()
        _db_pool = None
