"""
Database connection pool for improved performance and resource management.
"""
import aioodbc
import asyncio
from typing import Optional
import logging
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class DatabasePool:
    """Async database connection pool with automatic reconnection."""
    
    def __init__(self, connection_string: str, min_size: int = 5, max_size: int = 20):
        self.connection_string = connection_string
        self.min_size = min_size
        self.max_size = max_size
        self._pool: Optional[aioodbc.Pool] = None
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize the connection pool."""
        async with self._lock:
            if self._pool is None:
                try:
                    self._pool = await aioodbc.create_pool(
                        dsn=self.connection_string,
                        minsize=self.min_size,
                        maxsize=self.max_size,
                        echo=False
                    )
                    logger.info(f"Database pool initialized with {self.min_size}-{self.max_size} connections")
                except Exception as e:
                    logger.error(f"Failed to initialize database pool: {e}")
                    raise
    
    async def close(self):
        """Close the connection pool."""
        if self._pool:
            self._pool.close()
            await self._pool.wait_closed()
            self._pool = None
            logger.info("Database pool closed")
    
    @asynccontextmanager
    async def get_connection(self):
        """Get a connection from the pool."""
        if self._pool is None:
            await self.initialize()
        
        async with self._pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"Database operation failed: {e}")
                raise
    
    async def execute_query(self, query: str, params: tuple = None):
        """Execute a query and return results as a list of dictionaries."""
        async with self.get_connection() as conn:
            async with conn.cursor() as cursor:
                if params:
                    await cursor.execute(query, params)
                else:
                    await cursor.execute(query)
                
                if cursor.description:
                    rows = await cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description]
                    return [dict(zip(columns, row)) for row in rows]
                return []

# Global pool instance
_db_pool: Optional[DatabasePool] = None

async def get_db_pool(connection_string: str) -> DatabasePool:
    """Get or create the global database pool."""
    global _db_pool
    if _db_pool is None:
        _db_pool = DatabasePool(connection_string)
        await _db_pool.initialize()
    return _db_pool

async def close_db_pool():
    """Close the global database pool."""
    global _db_pool
    if _db_pool:
        await _db_pool.close()
        _db_pool = None
