import sys
import os
import sentry_sdk
import asyncio
import logging
import time
from contextlib import asynccontextmanager

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordBearer
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from typing import Annotated
from routers.image_recognition import router as image_recognition_router
from prometheus_fastapi_instrumentator import Instrumentator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
# OAuth2PasswordBearer for extracting Bearer Token
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Function to validate the token
def validate_token(token: Annotated[str, Depends(oauth2_scheme)]):
    if token != "a4f9d3b2a3e4f1b5c9d3a7e9f1a3b4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f0a1b2":  # Replace with actual token validation logic
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return token

tags_metadata = [
    {
        "name": "Health",
        "description": "Endpoints related to health checks."
    },
    {
        "name": "IR",
        "description": "Endpoints related to image recognition complanice and inference."
    },
]

# Application lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown."""
    # Startup
    logger.info("Starting Image Recognition API")
    app.state.start_time = time.time()
    # Pre-warm any global resources here
    yield
    # Shutdown
    logger.info("Shutting down Image Recognition API")

app = FastAPI(
    title="Image Recognition API",
    description="High-performance image recognition API with YOLOv5",
    version="2.0.0",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    openapi_url="/openapi.json" if os.getenv("ENVIRONMENT") != "production" else None,
    openapi_tags=tags_metadata,
    lifespan=lifespan
)

# Add performance middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if os.getenv("ENVIRONMENT") != "production" else [],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Security middleware
if os.getenv("ENVIRONMENT") == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.yourdomain.com", "localhost"]
    )

sentry_sdk.init(
    dsn="http://<EMAIL>:8000/8", 
    traces_sample_rate=1.0,
    environment= os.environ.get("AppSettings__Deployment")
)

Instrumentator().instrument(app).expose(app)

@app.get("/health", tags=["Health"])
def health():
    return {"status": "ok"}

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint for load balancers and monitoring."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "2.0.0"
    }

# Performance metrics endpoint
@app.get("/metrics", tags=["Health"])
async def get_metrics():
    """Get application performance metrics."""
    from Helpers.model_cache import model_cache
    from Helpers.db_pool import _db_pool

    metrics = {
        "model_cache": model_cache.get_stats(),
        "database": _db_pool.get_stats() if _db_pool else {"status": "not_initialized"},
        "uptime": time.time() - app.state.start_time if hasattr(app.state, 'start_time') else 0
    }

    return metrics

# Register Routers (with authentication applied globally)
app.include_router(image_recognition_router, dependencies=[Depends(validate_token)])
