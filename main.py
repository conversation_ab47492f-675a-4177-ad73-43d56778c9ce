import sys
import os
import sentry_sdk
import logging
from contextlib import asynccontextmanager

parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)

from fastapi import FastAPI, HTTPException, Depends, status
from routers.image_recognition import router as image_recognition_router
from prometheus_fastapi_instrumentator import Instrumentator
from utils.logging_config import setup_logging, performance_metrics
from Helpers.db_pool import close_db_pool

# Setup enhanced logging
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown tasks."""
    # Startup
    logger.info("Starting Image Recognition API")
    yield
    # Shutdown
    logger.info("Shutting down Image Recognition API")
    await close_db_pool()

tags_metadata = [
    {
        "name": "Health",
        "description": "Endpoints related to health checks."
    },
    {
        "name": "IR",
        "description": "Endpoints related to image recognition complanice and inference."
    },
]

app = FastAPI(
    title="Image Recognition API",
    description="High-performance image recognition API with YOLOv5 models",
    version="2.0.0",
    docs_url="/docs",  # URL for Swagger UI
    redoc_url="/redoc",  # URL for ReDoc UI
    openapi_url="/openapi.json",  # URL for OpenAPI schema
    openapi_tags=tags_metadata,
    lifespan=lifespan
)

sentry_sdk.init(
    dsn="http://<EMAIL>:8000/8", 
    traces_sample_rate=1.0,
    environment= os.environ.get("AppSettings__Deployment")
)

Instrumentator().instrument(app).expose(app)

@app.get("/health", tags=["Health"])
def health():
    return {"status": "ok"}

# Add metrics endpoint
@app.get("/metrics/performance", tags=["Health"])
async def get_performance_metrics():
    """Get application performance metrics."""
    return performance_metrics.get_metrics()

# Register Routers (authentication is now handled in individual routes)
app.include_router(image_recognition_router)
