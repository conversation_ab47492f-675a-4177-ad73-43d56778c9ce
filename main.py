import sys
import os
import sentry_sdk
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from fastapi import FastAPI, HTTPException,Depends,status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from typing import Annotated
from routers.image_recognition import router as image_recognition_router
from prometheus_fastapi_instrumentator import Instrumentator
# OAuth2PasswordBearer for extracting Bearer Token
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Function to validate the token
def validate_token(token: Annotated[str, Depends(oauth2_scheme)]):
    if token != "a4f9d3b2a3e4f1b5c9d3a7e9f1a3b4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f0a1b2":  # Replace with actual token validation logic
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return token

tags_metadata = [
    {
        "name": "Health",
        "description": "Endpoints related to health checks."
    },
    {
        "name": "IR",
        "description": "Endpoints related to image recognition complanice and inference."
    },
]

app = FastAPI(
    title="My FastAPI App",
    description="API documentation",
    version="1.0.0",
    docs_url="/docs",  # URL for Swagger UI
    redoc_url="/redoc",  # URL for ReDoc UI
    openapi_url="/openapi.json",  # URL for OpenAPI schema
    openapi_tags=tags_metadata
)

sentry_sdk.init(
    dsn="http://<EMAIL>:8000/8", 
    traces_sample_rate=1.0,
    environment= os.environ.get("AppSettings__Deployment")
)

Instrumentator().instrument(app).expose(app)

@app.get("/health", tags=["Health"])
def health():
    return {"status": "ok"}

# Register Routers (with authentication applied globally)
app.include_router(image_recognition_router, dependencies=[Depends(validate_token)])
