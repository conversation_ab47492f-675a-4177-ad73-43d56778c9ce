import asyncio
import json
import os
import uuid
import sentry_sdk
import logging
from contextlib import asynccontextmanager
from fastapi import APIRouter, HTTPException, Depends, Request
from config import get_config
from Helpers.blob_helper import BlobReader
from Helpers.compliance import annotate_image
from Helpers.db_operations import get_product_details
from Helpers.inference import detect_products_and_classify, load_model
from data_models.ApiRequestModel import ImageRecognitionRequestModel, ImageRecognitionResponseModel
from config import get_blob_reader
from utils.logging_config import log_performance, performance_metrics
from security.auth import validate_token_dependency

logger = logging.getLogger(__name__)

router = APIRouter()
@router.post("/ir/detection", response_model=ImageRecognitionResponseModel, tags=["IR"])
@log_performance("image_recognition_endpoint")
async def image_recognition(
    request: ImageRecognitionRequestModel,
    blob_reader: BlobReader = Depends(get_blob_reader),
    token: str = Depends(validate_token_dependency)
):
    # Track request metrics
    await performance_metrics.increment('request_count')
    temp_files = []  # Track temporary files for cleanup

    try:
        # Validate request parameters
        if not request.image_url:
            raise HTTPException(status_code=400, detail="Image URL is required")
        if request.company_id <= 0:
            raise HTTPException(status_code=400, detail="Invalid company ID")
        if request.logic_id <= 0:
            raise HTTPException(status_code=400, detail="Invalid logic ID")

        logger.info(f"Processing image recognition request for company {request.company_id}, logic {request.logic_id}")

        # Read image from blob with better error handling
        try:
            image_path = await blob_reader.read_image_from_blob("images", request.image_url)
            if image_path is None:
                raise HTTPException(status_code=400, detail="Could not read image from blob URL.")
            temp_files.append(image_path)
            logger.debug(f"Downloaded image to {image_path}")
        except Exception as e:
            logger.error(f"Failed to download image {request.image_url}: {e}")
            raise HTTPException(status_code=400, detail=f"Failed to download image: {str(e)}")

        # Download model files and configurations concurrently
        try:
            object_model_path, sku_model_path, product_file_path, args_file_path = await asyncio.gather(
                blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, "_obj.pt"),
                blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, ".pt"),
                blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_class.txt"),
                blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_args.txt")
            )

            # Track downloaded files for cleanup
            for file_path in [object_model_path, sku_model_path, product_file_path, args_file_path]:
                if file_path:
                    temp_files.append(file_path)

        except Exception as e:
            logger.error(f"Failed to download model files: {e}")
            raise HTTPException(status_code=500, detail="Failed to download required model files")

        sku_model, object_model = await asyncio.gather(
        asyncio.to_thread(load_model, sku_model_path),
        asyncio.to_thread(load_model, object_model_path)
        )
        
        model_args = {}
        try:
            with open(args_file_path, 'r') as f:
                model_args = json.load(f)
        except Exception:
            model_args = {}
            
        for model in [sku_model, object_model]:
            model.conf = model_args.get("conf", 0.4)
            model.iou = model_args.get("iou", 0.45)
            model.agnostic = model_args.get("agnostic", False)
            model.multi_label = model_args.get("multi_label", False)
            model.max_det = model_args.get("max_det", 1000)
            model.imgsz = model_args.get("imgsz", [640, 640])
            model.augment = model_args.get("augment", False)
            model.classes = model_args.get("classes", None)
            model.visualize = model_args.get("visualize", False)
            model.half = model_args.get("half", False)
            model.dnn = model_args.get("dnn", False)
            model.vid_stride = model_args.get("vid_stride", 1)

        product_ids_list = blob_reader.read_txt_to_list(product_file_path)
        db_conn_str: str = await get_config()
        product_df = await get_product_details(product_ids_list,request.company_id,db_conn_str)
        product_dict = {item['Id']: item['Name'] for item in product_df.to_dict(orient='records')}
        ordered_product_names = [product_dict[int(pid)] for pid in product_ids_list if int(pid) in product_dict]

        sku_detections,object_detections = detect_products_and_classify(image_path, object_model, sku_model, ordered_product_names)

        obj_image,sku_image,sku_detection_result,obj_detection_result = annotate_image(image_path, sku_detections,object_detections,product_dict)
        
        # Save annotated image
        object_new_guid = f"{str(uuid.uuid4().hex)}"
        sku_new_guid = f"{str(uuid.uuid4().hex)}"
        await asyncio.gather(
         blob_reader.upload_image_to_blob("images", f"{object_new_guid}",obj_image,"image/png"),
         blob_reader.upload_image_to_blob("images", f"{sku_new_guid}",sku_image,"image/png")
         )
        
        # Clean up temporary files
        await _cleanup_temp_files(temp_files)

        result = ImageRecognitionResponseModel(
            sku_image_path=sku_new_guid,
            obj_image_path=object_new_guid,
            sku_detection_result=sku_detection_result,
            obj_detection_result=obj_detection_result
        )

        logger.info(f"Successfully processed image recognition request")
        return result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        await _cleanup_temp_files(temp_files)
        raise
    except Exception as e:
        # Handle unexpected errors
        await performance_metrics.increment('error_count')
        await _cleanup_temp_files(temp_files)

        logger.error(f"Unexpected error in image recognition: {e}", exc_info=True)
        sentry_sdk.capture_exception(e)

        raise HTTPException(
            status_code=500,
            detail="Internal server error occurred during image processing"
        )

async def _cleanup_temp_files(file_paths: list):
    """Clean up temporary files safely."""
    for file_path in file_paths:
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"Cleaned up temporary file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary file {file_path}: {e}")