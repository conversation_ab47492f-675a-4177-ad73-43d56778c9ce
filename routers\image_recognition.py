import asyncio
import json
import os
import uuid
import sentry_sdk
from fastapi import APIRouter, HTTPException,Depends
from config import get_config
from Helpers.blob_helper import BlobReader
from Helpers.compliance import annotate_image
from Helpers.db_operations import get_product_details
from Helpers.inference import  detect_products_and_classify, load_model
from data_models.ApiRequestModel import ImageRecognitionRequestModel, ImageRecognitionResponseModel
from config import get_blob_reader

router = APIRouter()
@router.post("/ir/detection",response_model=ImageRecognitionResponseModel, tags=["IR"])
async def image_recognition(request: ImageRecognitionRequestModel, blob_reader: BlobReader = Depends(get_blob_reader)):
    try:
        # Read image from blob
        image_path = await blob_reader.read_image_from_blob("images",request.image_url)
        if image_path is None:
            raise HTTPException(status_code=400, detail="Could not read image from blob URL.")

        object_model_path, sku_model_path, product_file_path,args_file_path = await asyncio.gather(
        blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, "_obj.pt"),
        blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, ".pt"),
        blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_class.txt"),
        blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_args.txt")
        )

        sku_model, object_model = await asyncio.gather(
        asyncio.to_thread(load_model, sku_model_path),
        asyncio.to_thread(load_model, object_model_path)
        )
        
        model_args = {}
        try:
            with open(args_file_path, 'r') as f:
                model_args = json.load(f)
        except Exception:
            model_args = {}
            
        for model in [sku_model, object_model]:
            model.conf = model_args.get("conf", 0.4)
            model.iou = model_args.get("iou", 0.45)
            model.agnostic = model_args.get("agnostic", False)
            model.multi_label = model_args.get("multi_label", False)
            model.max_det = model_args.get("max_det", 1000)
            model.imgsz = model_args.get("imgsz", [640, 640])
            model.augment = model_args.get("augment", False)
            model.classes = model_args.get("classes", None)
            model.visualize = model_args.get("visualize", False)
            model.half = model_args.get("half", False)
            model.dnn = model_args.get("dnn", False)
            model.vid_stride = model_args.get("vid_stride", 1)

        product_ids_list = blob_reader.read_txt_to_list(product_file_path)
        db_conn_str: str = await get_config()
        product_df = await get_product_details(product_ids_list,request.company_id,db_conn_str)
        product_dict = {item['Id']: item['Name'] for item in product_df.to_dict(orient='records')}
        ordered_product_names = [product_dict[int(pid)] for pid in product_ids_list if int(pid) in product_dict]

        sku_detections,object_detections = detect_products_and_classify(image_path, object_model, sku_model, ordered_product_names)

        obj_image,sku_image,sku_detection_result,obj_detection_result = annotate_image(image_path, sku_detections,object_detections,product_dict)
        
        # Save annotated image
        object_new_guid = f"{str(uuid.uuid4().hex)}"
        sku_new_guid = f"{str(uuid.uuid4().hex)}"
        await asyncio.gather(
         blob_reader.upload_image_to_blob("images", f"{object_new_guid}",obj_image,"image/png"),
         blob_reader.upload_image_to_blob("images", f"{sku_new_guid}",sku_image,"image/png")
         )
        
        os.remove(image_path)
        result = ImageRecognitionResponseModel(
            sku_image_path=sku_new_guid,
            obj_image_path=object_new_guid,
            sku_detection_result=sku_detection_result,
            obj_detection_result= obj_detection_result
                )
        return result
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise e