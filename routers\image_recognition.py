import asyncio
import json
import os
import uuid
import time
import logging
import sentry_sdk
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from config import get_config
from Helpers.blob_helper import BlobReader
from Helpers.compliance import annotate_image
from Helpers.db_operations import get_product_details
from Helpers.inference import detect_products_and_classify, load_model
from Helpers.model_cache import model_cache
from data_models.ApiRequestModel import ImageRecognitionRequestModel, ImageRecognitionResponseModel
from config import get_blob_reader

logger = logging.getLogger(__name__)

router = APIRouter()

async def cleanup_temp_files(file_paths: list):
    """Clean up temporary files in background."""
    for file_path in file_paths:
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"Cleaned up: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup {file_path}: {e}")

@router.post("/ir/detection", response_model=ImageRecognitionResponseModel, tags=["IR"])
async def image_recognition(
    request: ImageRecognitionRequestModel,
    background_tasks: BackgroundTasks,
    blob_reader: BlobReader = Depends(get_blob_reader)
):
    """Optimized image recognition endpoint with improved error handling and performance."""
    start_time = time.time()
    temp_files = []

    try:
        # Input validation
        if not request.image_url:
            raise HTTPException(status_code=400, detail="Image URL is required")
        if request.company_id <= 0:
            raise HTTPException(status_code=400, detail="Invalid company ID")
        if request.logic_id <= 0:
            raise HTTPException(status_code=400, detail="Invalid logic ID")

        logger.info(f"Processing request for company {request.company_id}, logic {request.logic_id}")

        # Download image and model files concurrently
        download_tasks = [
            blob_reader.read_image_from_blob("images", request.image_url),
            blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, "_obj.pt"),
            blob_reader.GetWeightsFromBlob("weights", request.logic_id, request.company_id, ".pt"),
            blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_class.txt"),
            blob_reader.GetProductFromBlob("weights", request.logic_id, request.company_id, "_args.txt")
        ]

        image_path, object_model_path, sku_model_path, product_file_path, args_file_path = await asyncio.gather(*download_tasks)

        # Track files for cleanup
        temp_files = [f for f in [image_path, object_model_path, sku_model_path, product_file_path, args_file_path] if f]

        if image_path is None:
            raise HTTPException(status_code=400, detail="Could not read image from blob URL.")

        # Load models concurrently with caching
        sku_model, object_model = await asyncio.gather(
            load_model(sku_model_path),
            load_model(object_model_path)
        )

        # Load and apply model arguments
        model_args = {}
        if args_file_path and os.path.exists(args_file_path):
            try:
                with open(args_file_path, 'r') as f:
                    model_args = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load model args: {e}")

        # Apply model configuration
        for model in [sku_model, object_model]:
            model.conf = model_args.get("conf", 0.4)
            model.iou = model_args.get("iou", 0.45)
            model.agnostic = model_args.get("agnostic", False)
            model.multi_label = model_args.get("multi_label", False)
            model.max_det = model_args.get("max_det", 1000)
            model.imgsz = model_args.get("imgsz", [640, 640])
            model.augment = model_args.get("augment", False)
            model.classes = model_args.get("classes", None)
            model.visualize = model_args.get("visualize", False)
            model.half = model_args.get("half", False)
            model.dnn = model_args.get("dnn", False)
            model.vid_stride = model_args.get("vid_stride", 1)

        # Get product details concurrently with inference preparation
        product_ids_list = blob_reader.read_txt_to_list(product_file_path)
        db_conn_str = await get_config()

        # Run database query and inference concurrently
        product_df_task = asyncio.create_task(
            get_product_details(product_ids_list, request.company_id, db_conn_str)
        )

        # Run inference
        sku_detections, object_detections = detect_products_and_classify(
            image_path, object_model, sku_model, product_ids_list
        )

        # Wait for database results
        product_df = await product_df_task
        product_dict = {item['Id']: item['Name'] for item in product_df.to_dict(orient='records')}
        ordered_product_names = [product_dict[int(pid)] for pid in product_ids_list if int(pid) in product_dict]

        # Update detections with proper product names
        updated_sku_detections = []
        for box, label_idx, conf in sku_detections:
            if isinstance(label_idx, int) and 0 <= label_idx < len(ordered_product_names):
                label = ordered_product_names[label_idx]
            else:
                label = str(label_idx)
            updated_sku_detections.append((box, label, conf))

        # Annotate images
        obj_image, sku_image, sku_detection_result, obj_detection_result = annotate_image(
            image_path, updated_sku_detections, object_detections, product_dict
        )

        # Upload annotated images concurrently
        object_new_guid = uuid.uuid4().hex
        sku_new_guid = uuid.uuid4().hex

        await asyncio.gather(
            blob_reader.upload_image_to_blob("images", object_new_guid, obj_image, "image/png"),
            blob_reader.upload_image_to_blob("images", sku_new_guid, sku_image, "image/png")
        )

        # Schedule cleanup in background
        background_tasks.add_task(cleanup_temp_files, temp_files)

        # Log performance
        processing_time = time.time() - start_time
        logger.info(f"Request processed in {processing_time:.2f}s")

        return ImageRecognitionResponseModel(
            sku_image_path=sku_new_guid,
            obj_image_path=object_new_guid,
            sku_detection_result=sku_detection_result,
            obj_detection_result=obj_detection_result
        )

    except HTTPException:
        # Clean up on HTTP errors
        background_tasks.add_task(cleanup_temp_files, temp_files)
        raise
    except Exception as e:
        # Clean up on unexpected errors
        background_tasks.add_task(cleanup_temp_files, temp_files)
        logger.error(f"Unexpected error in image recognition: {e}", exc_info=True)
        sentry_sdk.capture_exception(e)
        raise HTTPException(status_code=500, detail="Internal server error occurred during image processing")

@router.get("/cache/stats", tags=["IR"])
async def get_cache_stats():
    """Get model cache statistics."""
    return model_cache.get_stats()