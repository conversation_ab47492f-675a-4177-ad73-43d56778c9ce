from typing import List
from pydantic import BaseModel

class PredictionDataSet(BaseModel):
    Product: str
    Label: str
    Count: int
    Id: str

class ImageRecognitionRequestModel(BaseModel):
    image_url: str
    company_id: int
    logic_id: int

class ImageRecognitionResponseModel(BaseModel):
    obj_image_path: str
    sku_image_path: str
    sku_detection_result: List[PredictionDataSet]
    obj_detection_result: List[PredictionDataSet]


