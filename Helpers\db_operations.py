from typing import List
import aioodbc
import pandas as pd

async def get_data_from_db(db_conn_str, query):
    # Establish the connection
    async with aioodbc.connect(dsn=db_conn_str) as conn:
        # Create a cursor
        async with conn.cursor() as cursor:
            # Execute the query
            await cursor.execute(query)
            # Fetch all rows from the executed query
            if cursor.description:
                rows = await cursor.fetchall()
                # Fetch column names from the cursor description
                columns = [desc[0] for desc in cursor.description]
                
                # Ensure the number of columns in rows matches the number of column names
                if len(rows) > 0 and len(columns) != len(rows[0]):
                    raise ValueError(f"Number of columns mismatch: expected {len(columns)} but got {len(rows[0])}")

                # Ensure that each row is a tuple
                rows = [tuple(row) for row in rows]

                # Construct a pandas DataFrame from the fetched data
                df = pd.DataFrame(rows, columns=columns)
                return df

async def get_product_details(productIds: List[str],companyId:int,db_conn_str:str):
    productIds.append('0')
    productList = ','.join(map(str, productIds))
    query = f'Select Name,Id from FACompanyProducts where companyId = {companyId} and Id in ({productList})'
    df = await get_data_from_db(db_conn_str, query)
    return df