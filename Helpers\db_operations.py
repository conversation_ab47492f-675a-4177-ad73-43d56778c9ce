from typing import List
import aioodbc
import pandas as pd
import logging
from .db_pool import get_db_pool

logger = logging.getLogger(__name__)

async def get_data_from_db(db_conn_str, query):
    # Establish the connection
    async with aioodbc.connect(dsn=db_conn_str) as conn:
        # Create a cursor
        async with conn.cursor() as cursor:
            # Execute the query
            await cursor.execute(query)
            # Fetch all rows from the executed query
            if cursor.description:
                rows = await cursor.fetchall()
                # Fetch column names from the cursor description
                columns = [desc[0] for desc in cursor.description]
                
                # Ensure the number of columns in rows matches the number of column names
                if len(rows) > 0 and len(columns) != len(rows[0]):
                    raise ValueError(f"Number of columns mismatch: expected {len(columns)} but got {len(rows[0])}")

                # Ensure that each row is a tuple
                rows = [tuple(row) for row in rows]

                # Construct a pandas DataFrame from the fetched data
                df = pd.DataFrame(rows, columns=columns)
                return df

async def get_product_details(productIds: List[str], companyId: int, db_conn_str: str):
    """Get product details with improved performance and security."""
    if not productIds:
        return pd.DataFrame(columns=['Name', 'Id'])

    # Add '0' for fallback and remove duplicates
    unique_ids = list(set(productIds + ['0']))

    try:
        # Use connection pool for better performance
        db_pool = await get_db_pool(db_conn_str)

        # Use parameterized query to prevent SQL injection
        placeholders = ','.join(['?' for _ in unique_ids])
        query = f'SELECT Name, Id FROM FACompanyProducts WHERE companyId = ? AND Id IN ({placeholders})'

        results = await db_pool.execute_query(query, (companyId, *unique_ids))

        # Convert to DataFrame
        df = pd.DataFrame(results)
        logger.info(f"Retrieved {len(df)} product details for company {companyId}")
        return df

    except Exception as e:
        logger.error(f"Error retrieving product details: {e}")
        # Fallback to original method
        return await get_data_from_db(db_conn_str,
            f'SELECT Name,Id FROM FACompanyProducts WHERE companyId = {companyId} AND Id IN ({",".join(map(str, unique_ids))})'
        )