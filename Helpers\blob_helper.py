from io import BytesIO
from PIL import Image
from azure.storage.blob import ContentSettings
from azure.storage.blob.aio import BlobServiceClient
import os
import asyncio
import cv2

class BlobReader:
    def __init__(self, connection_string):
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)

    async def read_image_from_blob(self, container_name, blob_path):
        """
        Asynchronously reads an image from a blob container, saves it locally, and returns the local image path.
        """
        return await self._download_blob(container_name, blob_path, "temp_images")

    async def read_pt_from_blob(self, container_name, blob_path):
        """
        Asynchronously reads a .pt file from a blob container, saves it locally, and returns the local file path.
        """
        try:
            local_file_path = os.path.join("temp_models", blob_path.replace("/", "_"))
            
            if os.path.exists(local_file_path):
                print(f"File already exists: {local_file_path}")
                return local_file_path
            
            return await self._download_blob(container_name, blob_path, "temp_models",delete_after_seconds=24*60*60)
        except Exception as e:
            print(f"Error reading .pt file from blob: {e}")
            return None

    async def read_txt_from_blob(self, container_name, blob_path):
        """
        Asynchronously reads a .txt file from a blob container, saves it locally, and returns the local file path.
        """
        try:
            local_file_path = os.path.join("temp_file", blob_path.replace("/", "_"))
            
            if os.path.exists(local_file_path):
                print(f"File already exists: {local_file_path}")
                return local_file_path
            
            return await self._download_blob(container_name, blob_path, "temp_file",delete_after_seconds=6*60*60)
        except Exception as e:
            print(f"Error reading .txt file from blob: {e}")
            return None

    async def _download_blob(self, container_name, blob_path, local_dir,delete_after_seconds=None):
        """
        Asynchronously downloads a blob from the specified container and blob path.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=blob_path)

            # Download the blob data asynchronously
            download_stream = await blob_client.download_blob()
            blob_data = await download_stream.readall()

            local_file_path = os.path.join(local_dir, blob_path.replace("/", "_"))
            os.makedirs(local_dir, exist_ok=True)

            # Write data asynchronously
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(None, self._write_file, local_file_path, blob_data)
            
            if delete_after_seconds:
                asyncio.create_task(self._delete_file_after_delay(local_file_path, delete_after_seconds))

            return local_file_path
        except Exception as e:
            print(f"Error downloading blob: {e}")
            return None

    async def _delete_file_after_delay(self, file_path: str, delay: int):
        """Deletes the specified file after a delay."""
        await asyncio.sleep(delay)
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"File {file_path} deleted successfully.")
            else:
                print(f"File {file_path} does not exist.")
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")
            
    def _write_file(self, file_path, data):
        """Write data to file (synchronous but executed in a thread pool)."""
        with open(file_path, "wb") as f:
            f.write(data)

    async def GetWeightsFromBlob(self, container_name, logic_id, company_id, extension):
        """
        Asynchronously checks if a blob exists and downloads it if available.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"{company_id}_{logic_id}{extension}")
            if await blob_client.exists():
                return await self.read_pt_from_blob(container_name, f"{company_id}_{logic_id}{extension}")

            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"{company_id}{extension}")
            if await blob_client.exists():
                return await self.read_pt_from_blob(container_name, f"{company_id}{extension}")

            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"best{extension}")
            if await blob_client.exists():
                return await self.read_pt_from_blob(container_name, f"best{extension}")

        except Exception as e:
            print(f"Error checking if blob exists: {e}")
            raise e

    async def GetProductFromBlob(self, container_name, logic_id, company_id, extension):
        """
        Asynchronously checks if a product blob exists and downloads it if available.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"{company_id}_{logic_id}{extension}")
            if await blob_client.exists():
                return await self.read_txt_from_blob(container_name, f"{company_id}_{logic_id}{extension}")

            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"{company_id}{extension}")
            if await blob_client.exists():
                return await self.read_txt_from_blob(container_name, f"{company_id}{extension}")

            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=f"best{extension}")
            if await blob_client.exists():
                return await self.read_txt_from_blob(container_name, f"best{extension}")

        except Exception as e:
            print(f"Error checking if blob exists: {e}")
            raise e

    @staticmethod
    def read_txt_to_list(file_path):
        """Safely read a list from a text file using JSON or literal_eval."""
        import json
        import ast

        try:
            with open(file_path, 'r') as file:
                content = file.read().strip()

                # Try JSON first (most secure)
                try:
                    result = json.loads(content)
                    if isinstance(result, list):
                        return result
                    else:
                        raise ValueError("JSON content is not a list")
                except json.JSONDecodeError:
                    # Fallback to ast.literal_eval (safe eval alternative)
                    try:
                        result = ast.literal_eval(content)
                        if isinstance(result, list):
                            return result
                        else:
                            raise ValueError("File content is not a list")
                    except (ValueError, SyntaxError) as e:
                        raise ValueError(f"Could not parse file content as a list: {e}")

        except Exception as e:
            print(f"Error reading file: {e}")
            raise e

    async def upload_image_to_blob(self, container_name, blob_path, image_array, content_type):
        """
        Asynchronously uploads an ndarray image to the specified blob container and path.

        Args:
            container_name (str): Name of the container.
            blob_path (str): Path to store the blob.
            image_array (ndarray): Numpy array representing the image.
            content_type (str): Content type of the image (e.g., 'image/png', 'image/jpeg').

        Returns:
            str: Blob URL if successful, None otherwise.
        """
        try:
            blob_client = self.blob_service_client.get_blob_client(container=container_name, blob=blob_path)

            # Convert ndarray to an image and write to a BytesIO buffer
            buffer = BytesIO()
            image = Image.fromarray(cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB))
            image_format = content_type.split("/")[-1].upper()
            image.save(buffer, format=image_format)
            buffer.seek(0)  # Reset the buffer position to the beginning

            # Upload the buffer data to the blob
            await blob_client.upload_blob(
                buffer,
                overwrite=True,
                content_settings=ContentSettings(content_type=content_type)
            )

            blob_url = f"{self.blob_service_client.url}/{container_name}/{blob_path}"
            print(f"Uploaded to: {blob_url}")
            return blob_url
        except Exception as e:
            print(f"Error uploading image to blob: {e}")
            raise e
