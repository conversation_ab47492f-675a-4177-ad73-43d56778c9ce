import configparser
import os
from Helpers.blob_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

async def get_config():
    config = configparser.ConfigParser()
    
    config.read("config_details/db_config.ini")
    server = config['Database']['server']
    database = config['Database']['database']
    username = config['Database']['username']
    pwd = config['Database']['password']
    encrypt = config['Database']['encrypt']
    trusted = config['Database']['trusted_connection']
    conn_timeout = config['Database']['connection_timeout']
    pool_size = config['Database']['max_pool_size']

    db_conn_str = f"DRIVER=ODBC Driver 18 for SQL Server;" \
              f"SERVER={server};" \
              f"DATABASE={database};" \
              f"UID={username};" \
              f"PWD={pwd};" \
              f"Trusted_Connection={trusted};" \
              f"ENCRYPT={encrypt};" \
              f"Connection Timeout={conn_timeout};" \
              f"Max Pool Size={pool_size};"

    return  db_conn_str

# Initialize BlobReader at startup
CONNECTION_STRING = os.environ.get("ConnectionStrings__ImageStorageConnectionString")
#CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=imagedetectionv2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
if not CONNECTION_STRING:
    raise ValueError(f"Azure storage connection string not found in environment variables.")

blob_reader = BlobReader(CONNECTION_STRING)

def get_blob_reader():
    return blob_reader