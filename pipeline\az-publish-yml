trigger:
  branches:
    include:
      - $(Build.SourceBranchName)  # Dynamically takes the branch name

pool:
  name: 'scaleset-agent'

stages:
  - stage: Build_And_Push_ImageRecognition_Api
    displayName: "Build and Push ImageRecognition API Docker Image"
    jobs:
      - job: BuildAndPushImageRecognitionAPI
        displayName: "Build & Push ImageRecognition API Docker Image"
        steps:
          - task: Docker@2
            inputs:
              containerRegistry: 'FAi Container Registry Service Connector'
              repository: 'imagerecognitiondetection-$(environment)'
              command: 'buildAndPush'
              Dockerfile: '**/Dockerfile'
              tags: '$(Build.BuildNumber)'