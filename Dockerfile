# Multi-stage build for optimized image size
FROM python:3.11.11-slim-bullseye as builder

# Set environment variables for build optimization
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    unzip \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Microsoft ODBC driver
RUN curl -sSL -O https://packages.microsoft.com/config/debian/$(grep VERSION_ID /etc/os-release | cut -d '"' -f 2 | cut -d '.' -f 1)/packages-microsoft-prod.deb \
    && dpkg -i packages-microsoft-prod.deb \
    && rm packages-microsoft-prod.deb \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql18 unixodbc-dev \
    && rm -rf /var/lib/apt/lists/*

# Production stage
FROM python:3.11.11-slim-bullseye

# Copy ODBC driver from builder
COPY --from=builder /opt/microsoft /opt/microsoft
COPY --from=builder /usr/lib/x86_64-linux-gnu/odbc /usr/lib/x86_64-linux-gnu/odbc
COPY --from=builder /etc/odbcinst.ini /etc/odbcinst.ini

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="/app:/app/yolov5" \
    WORKERS=1 \
    MAX_WORKERS=4 \
    WEB_CONCURRENCY=1

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg \
    libsm6 \
    libxext6 \
    libglib2.0-0 \
    libxrender1 \
    libgomp1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create working directory
WORKDIR /app

# Install PyTorch CPU version (optimized for production)
RUN pip install --no-cache-dir torch==2.1.1+cpu torchvision==0.16.1+cpu --index-url https://download.pytorch.org/whl/cpu

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Download and setup YOLOv5
RUN curl -L -o yolov5.zip https://github.com/ultralytics/yolov5/archive/refs/tags/v7.0.zip \
    && unzip yolov5.zip \
    && mv yolov5-7.0 yolov5 \
    && rm yolov5.zip \
    && chown -R appuser:appuser yolov5

# Copy application code
COPY --chown=appuser:appuser . .

# Create directories for temporary files
RUN mkdir -p temp_images temp_models temp_file logs \
    && chown -R appuser:appuser temp_images temp_models temp_file logs

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Optimized startup command with performance tuning
CMD ["uvicorn", "main:app", \
    "--host", "0.0.0.0", \
    "--port", "8000", \
    "--workers", "1", \
    "--loop", "uvloop", \
    "--http", "httptools", \
    "--access-log", \
    "--log-level", "info", \
    "--timeout-keep-alive", "5", \
    "--timeout-graceful-shutdown", "30"]
