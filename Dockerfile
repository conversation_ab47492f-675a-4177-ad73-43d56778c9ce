# Use official Python base image
FROM python:3.11.11-slim-bullseye

# Set environment variables
# ENV ImageStorageConnectionString="DefaultEndpointsProtocol=https;AccountName=imagedetectionv2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

# Install system dependencies and ODBC driver (for aioodbc)
RUN apt update && apt install -y curl sudo unzip
RUN curl -sSL -O https://packages.microsoft.com/config/debian/$(grep VERSION_ID /etc/os-release | cut -d '"' -f 2 | cut -d '.' -f 1)/packages-microsoft-prod.deb
RUN sudo dpkg -i packages-microsoft-prod.deb
RUN rm packages-microsoft-prod.deb
RUN sudo apt-get update
RUN sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
RUN sudo apt-get install -y unixodbc-dev

# Create working directory
WORKDIR /app

RUN pip install torch==2.1.1+cpu torchvision==0.16.1+cpu --index-url https://download.pytorch.org/whl/cpu

COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

RUN rm -rf /var/lib/apt/lists/*

# Download YOLOv5 v7.0 (pre-ultralytics dependency)
RUN curl -L -o yolov5.zip https://github.com/ultralytics/yolov5/archive/refs/tags/v7.0.zip && \
    unzip yolov5.zip && mv yolov5-7.0 yolov5 && rm yolov5.zip

# Add YOLOv5 to PYTHONPATH
ENV PYTHONPATH="${PYTHONPATH}:/app/yolov5"


RUN apt-get update && apt-get install ffmpeg libsm6 libxext6  -y
# Copy project files
COPY . .

# Expose port for FastAPI
EXPOSE 8000

# Start FastAPI application using Uvicorn
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
