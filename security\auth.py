"""
Enhanced authentication and security utilities.
"""
import hashlib
import hmac
import time
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import Depends
import logging
import os

logger = logging.getLogger(__name__)

class EnhancedTokenValidator:
    """Enhanced token validation with rate limiting and logging."""
    
    def __init__(self):
        self.valid_tokens = set()
        self.failed_attempts = {}
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        
        # Load tokens from environment or secure storage
        self._load_valid_tokens()
    
    def _load_valid_tokens(self):
        """Load valid tokens from environment variables."""
        # Primary token
        primary_token = os.environ.get("API_PRIMARY_TOKEN")
        if primary_token:
            self.valid_tokens.add(primary_token)
        
        # Additional tokens (comma-separated)
        additional_tokens = os.environ.get("API_ADDITIONAL_TOKENS", "")
        if additional_tokens:
            for token in additional_tokens.split(","):
                if token.strip():
                    self.valid_tokens.add(token.strip())
        
        # Fallback to hardcoded token (for backward compatibility)
        if not self.valid_tokens:
            self.valid_tokens.add("a4f9d3b2a3e4f1b5c9d3a7e9f1a3b4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f0a1b2")
            logger.warning("Using fallback hardcoded token. Set API_PRIMARY_TOKEN environment variable.")
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client IP is rate limited."""
        if client_ip in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[client_ip]
            if attempts >= self.max_attempts:
                if time.time() - last_attempt < self.lockout_duration:
                    return True
                else:
                    # Reset after lockout period
                    del self.failed_attempts[client_ip]
        return False
    
    def _record_failed_attempt(self, client_ip: str):
        """Record a failed authentication attempt."""
        current_time = time.time()
        if client_ip in self.failed_attempts:
            attempts, _ = self.failed_attempts[client_ip]
            self.failed_attempts[client_ip] = (attempts + 1, current_time)
        else:
            self.failed_attempts[client_ip] = (1, current_time)
    
    def _clear_failed_attempts(self, client_ip: str):
        """Clear failed attempts for successful authentication."""
        if client_ip in self.failed_attempts:
            del self.failed_attempts[client_ip]
    
    def validate_token(self, token: str, client_ip: str = "unknown") -> bool:
        """
        Validate token with enhanced security features.
        
        Args:
            token: The token to validate
            client_ip: Client IP address for rate limiting
            
        Returns:
            bool: True if token is valid
            
        Raises:
            HTTPException: If token is invalid or rate limited
        """
        # Check rate limiting
        if self._is_rate_limited(client_ip):
            logger.warning(f"Rate limited authentication attempt from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many failed authentication attempts. Try again later.",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Validate token
        if not token or token not in self.valid_tokens:
            self._record_failed_attempt(client_ip)
            logger.warning(f"Invalid token attempt from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or missing token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Clear failed attempts on successful auth
        self._clear_failed_attempts(client_ip)
        logger.info(f"Successful authentication from {client_ip}")
        return True

# Global validator instance
token_validator = EnhancedTokenValidator()
security = HTTPBearer()

async def validate_token_dependency(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """FastAPI dependency for token validation."""
    token_validator.validate_token(credentials.credentials)
    return credentials.credentials
