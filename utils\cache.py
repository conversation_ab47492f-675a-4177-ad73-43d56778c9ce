"""
Caching utilities for improved performance.
"""
import asyncio
import hashlib
import json
import time
from typing import Any, Optional, Dict, Callable
import functools
import logging

logger = logging.getLogger(__name__)

class AsyncLRUCache:
    """Async LRU cache with TTL support."""
    
    def __init__(self, maxsize: int = 128, ttl: int = 3600):
        self.maxsize = maxsize
        self.ttl = ttl
        self.cache: Dict[str, tuple] = {}  # key -> (value, timestamp, access_count)
        self.access_order = []
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        async with self._lock:
            if key in self.cache:
                value, timestamp, access_count = self.cache[key]
                
                # Check TTL
                if time.time() - timestamp > self.ttl:
                    del self.cache[key]
                    if key in self.access_order:
                        self.access_order.remove(key)
                    return None
                
                # Update access order
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
                
                # Update access count
                self.cache[key] = (value, timestamp, access_count + 1)
                return value
            return None
    
    async def set(self, key: str, value: Any):
        """Set value in cache."""
        async with self._lock:
            current_time = time.time()
            
            # If cache is full, remove LRU item
            if len(self.cache) >= self.maxsize and key not in self.cache:
                lru_key = self.access_order.pop(0)
                del self.cache[lru_key]
            
            # Add/update item
            self.cache[key] = (value, current_time, 1)
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
    
    async def clear(self):
        """Clear all cache entries."""
        async with self._lock:
            self.cache.clear()
            self.access_order.clear()
    
    def stats(self) -> dict:
        """Get cache statistics."""
        return {
            "size": len(self.cache),
            "maxsize": self.maxsize,
            "ttl": self.ttl,
            "hit_rate": self._calculate_hit_rate()
        }
    
    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate."""
        if not self.cache:
            return 0.0
        
        total_accesses = sum(access_count for _, _, access_count in self.cache.values())
        return len(self.cache) / max(total_accesses, 1)

# Global cache instances
product_cache = AsyncLRUCache(maxsize=1000, ttl=1800)  # 30 minutes
model_args_cache = AsyncLRUCache(maxsize=100, ttl=3600)  # 1 hour

def cache_key_from_params(*args, **kwargs) -> str:
    """Generate cache key from function parameters."""
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    key_str = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_str.encode()).hexdigest()

def async_cached(cache_instance: AsyncLRUCache, key_func: Optional[Callable] = None):
    """Decorator for caching async function results."""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{cache_key_from_params(*args, **kwargs)}"
            
            # Try to get from cache
            cached_result = await cache_instance.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {func.__name__}")
            result = await func(*args, **kwargs)
            await cache_instance.set(cache_key, result)
            return result
        
        return wrapper
    return decorator

# Specific cache decorators
def cache_product_details(func):
    """Cache product details with company_id and product_ids as key."""
    def key_func(*args, **kwargs):
        # Extract company_id and product_ids for cache key
        if len(args) >= 2:
            product_ids, company_id = args[0], args[1]
        else:
            product_ids = kwargs.get('productIds', [])
            company_id = kwargs.get('companyId', 0)
        
        sorted_ids = sorted(str(pid) for pid in product_ids)
        return f"products:{company_id}:{':'.join(sorted_ids)}"
    
    return async_cached(product_cache, key_func)(func)

def cache_model_args(func):
    """Cache model arguments with logic_id and company_id as key."""
    def key_func(*args, **kwargs):
        # This would be used for caching model arguments
        logic_id = kwargs.get('logic_id', args[1] if len(args) > 1 else 0)
        company_id = kwargs.get('company_id', args[2] if len(args) > 2 else 0)
        return f"model_args:{company_id}:{logic_id}"
    
    return async_cached(model_args_cache, key_func)(func)
