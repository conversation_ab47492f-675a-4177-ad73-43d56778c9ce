"""
Azure-specific optimizations for blob storage and connection management.
"""
import asyncio
import logging
from typing import Optional, Dict, Any
from azure.storage.blob.aio import BlobServiceClient
from azure.core.exceptions import ResourceNotFoundError
import aiohttp
import time

logger = logging.getLogger(__name__)

class OptimizedBlobClient:
    """
    Optimized Azure Blob Storage client with connection pooling and retry logic.
    """
    
    def __init__(self, connection_string: str, max_connections: int = 100):
        self.connection_string = connection_string
        self.max_connections = max_connections
        self._client: Optional[BlobServiceClient] = None
        self._session: Optional[aiohttp.ClientSession] = None
        self._stats = {
            'downloads': 0,
            'uploads': 0,
            'errors': 0,
            'total_download_time': 0.0,
            'total_upload_time': 0.0
        }
    
    async def initialize(self):
        """Initialize the blob client with optimized connection settings."""
        if self._client is None:
            # Create optimized HTTP session
            connector = aiohttp.TCPConnector(
                limit=self.max_connections,
                limit_per_host=50,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(
                total=300,  # 5 minutes total timeout
                connect=30,  # 30 seconds connect timeout
                sock_read=60  # 60 seconds read timeout
            )
            
            self._session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
            
            # Initialize blob service client
            self._client = BlobServiceClient.from_connection_string(
                self.connection_string,
                session=self._session
            )
            
            logger.info("Optimized blob client initialized")
    
    async def close(self):
        """Close the blob client and session."""
        if self._client:
            await self._client.close()
            self._client = None
        
        if self._session:
            await self._session.close()
            self._session = None
        
        logger.info("Blob client closed")
    
    async def download_blob_with_retry(self, container_name: str, blob_name: str, 
                                     max_retries: int = 3, retry_delay: float = 1.0) -> Optional[bytes]:
        """Download blob with retry logic and performance tracking."""
        if not self._client:
            await self.initialize()
        
        start_time = time.time()
        
        for attempt in range(max_retries):
            try:
                blob_client = self._client.get_blob_client(
                    container=container_name, 
                    blob=blob_name
                )
                
                # Download with streaming for large files
                download_stream = await blob_client.download_blob()
                data = await download_stream.readall()
                
                # Update stats
                download_time = time.time() - start_time
                self._stats['downloads'] += 1
                self._stats['total_download_time'] += download_time
                
                logger.debug(f"Downloaded {blob_name} in {download_time:.2f}s")
                return data
                
            except ResourceNotFoundError:
                logger.error(f"Blob not found: {container_name}/{blob_name}")
                return None
            except Exception as e:
                self._stats['errors'] += 1
                logger.warning(f"Download attempt {attempt + 1} failed for {blob_name}: {e}")
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    logger.error(f"Failed to download {blob_name} after {max_retries} attempts")
                    return None
        
        return None
    
    async def upload_blob_with_retry(self, container_name: str, blob_name: str, data: bytes,
                                   content_type: str = "application/octet-stream",
                                   max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """Upload blob with retry logic and performance tracking."""
        if not self._client:
            await self.initialize()
        
        start_time = time.time()
        
        for attempt in range(max_retries):
            try:
                blob_client = self._client.get_blob_client(
                    container=container_name, 
                    blob=blob_name
                )
                
                # Upload with overwrite
                await blob_client.upload_blob(
                    data, 
                    overwrite=True,
                    content_settings={
                        'content_type': content_type
                    }
                )
                
                # Update stats
                upload_time = time.time() - start_time
                self._stats['uploads'] += 1
                self._stats['total_upload_time'] += upload_time
                
                logger.debug(f"Uploaded {blob_name} in {upload_time:.2f}s")
                return True
                
            except Exception as e:
                self._stats['errors'] += 1
                logger.warning(f"Upload attempt {attempt + 1} failed for {blob_name}: {e}")
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (2 ** attempt))
                else:
                    logger.error(f"Failed to upload {blob_name} after {max_retries} attempts")
                    return False
        
        return False
    
    async def batch_download(self, downloads: list) -> Dict[str, Optional[bytes]]:
        """Download multiple blobs concurrently."""
        if not self._client:
            await self.initialize()
        
        # Limit concurrent downloads to prevent overwhelming the service
        semaphore = asyncio.Semaphore(10)
        
        async def download_with_semaphore(container, blob_name):
            async with semaphore:
                return await self.download_blob_with_retry(container, blob_name)
        
        # Create download tasks
        tasks = []
        for container, blob_name in downloads:
            task = asyncio.create_task(
                download_with_semaphore(container, blob_name),
                name=f"download_{blob_name}"
            )
            tasks.append((blob_name, task))
        
        # Wait for all downloads
        results = {}
        for blob_name, task in tasks:
            try:
                results[blob_name] = await task
            except Exception as e:
                logger.error(f"Batch download failed for {blob_name}: {e}")
                results[blob_name] = None
        
        return results
    
    def get_stats(self) -> dict:
        """Get performance statistics."""
        avg_download_time = (
            self._stats['total_download_time'] / max(self._stats['downloads'], 1)
        )
        avg_upload_time = (
            self._stats['total_upload_time'] / max(self._stats['uploads'], 1)
        )
        
        return {
            **self._stats,
            'avg_download_time': avg_download_time,
            'avg_upload_time': avg_upload_time,
            'success_rate': (
                (self._stats['downloads'] + self._stats['uploads']) / 
                max(self._stats['downloads'] + self._stats['uploads'] + self._stats['errors'], 1)
            )
        }

# Global optimized blob client
_blob_client: Optional[OptimizedBlobClient] = None

async def get_optimized_blob_client(connection_string: str) -> OptimizedBlobClient:
    """Get or create the global optimized blob client."""
    global _blob_client
    if _blob_client is None:
        _blob_client = OptimizedBlobClient(connection_string)
        await _blob_client.initialize()
    return _blob_client

async def close_blob_client():
    """Close the global blob client."""
    global _blob_client
    if _blob_client:
        await _blob_client.close()
        _blob_client = None
