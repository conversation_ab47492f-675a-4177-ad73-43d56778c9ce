# from pathlib import Path
# import pathlib
import cv2
import torch
import functools
import hashlib
import os
from typing import Dict, Tuple
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Global model cache with LRU eviction
@functools.lru_cache(maxsize=10)
def _load_model_cached(model_path: str, model_hash: str):
    """
    Load model with caching based on file path and hash.
    The hash ensures cache invalidation when model file changes.
    """
    logger.info(f"Loading model from {model_path}")
    model = torch.hub.load('yolov5', 'custom', path=model_path, source='local', force_reload=True)
    return model

def get_file_hash(file_path: str) -> str:
    """Generate hash of file for cache invalidation."""
    try:
        with open(file_path, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash
    except Exception as e:
        logger.warning(f"Could not hash file {file_path}: {e}")
        return str(os.path.getmtime(file_path))  # Fallback to modification time

def parse_boxes(results):
    return [[int(b[0]), int(b[1]), int(b[2]), int(b[3])] for *b, _, _ in results.xyxy[0]]


def detect_products_and_classify(img_path: str, object_model, sku_model, sku_names: list) -> Tuple[list, list]:
    """
    Optimized two-stage detection with priority for SKU model:
    1. Get all SKU detections first - trust these classifications
    2. Add object detections only for objects not covered by SKU detections

    Optimizations:
    - Single image load and conversion
    - Vectorized operations where possible
    - Early validation
    """
    if not os.path.exists(img_path):
        raise FileNotFoundError(f"Image file not found: {img_path}")

    # Load image once and convert to RGB
    img_bgr = cv2.imread(img_path)
    if img_bgr is None:
        raise ValueError(f"Could not load image from {img_path}")

    img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)

    # Run both models concurrently if possible (depends on GPU memory)
    try:
        # First get all SKU model detections
        sku_results = sku_model(img_rgb)
        sku_boxes = parse_boxes(sku_results)

        # Vectorized extraction of classes and confidences
        sku_data = sku_results.xyxy[0].cpu().numpy() if hasattr(sku_results.xyxy[0], 'cpu') else sku_results.xyxy[0]
        sku_classes = sku_data[:, 5].astype(int)
        sku_classes_conf = sku_data[:, 4]

        # Create classified products list with bounds checking
        classified_products = []
        for i, (sku_box, cls, conf) in enumerate(zip(sku_boxes, sku_classes, sku_classes_conf)):
            if 0 <= cls < len(sku_names):
                label = sku_names[cls]
                classified_products.append((sku_box, label, float(conf)))
            else:
                logger.warning(f"Invalid class index {cls} for SKU model")

        # Now detect all product objects
        object_results = object_model(img_rgb)
        all_product_boxes = parse_boxes(object_results)

        # Create classified objects list
        classified_objects = [(obj_box, 'object') for obj_box in all_product_boxes]

        return classified_products, classified_objects

    except Exception as e:
        logger.error(f"Error during inference: {e}")
        raise

def load_model(model_path: str):
    """
    Load model with caching. Models are cached based on file path and content hash.
    Cache automatically invalidates when model file changes.
    """
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found: {model_path}")

    file_hash = get_file_hash(model_path)
    return _load_model_cached(model_path, file_hash)