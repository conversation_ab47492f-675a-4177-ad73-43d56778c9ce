# from pathlib import Path
# import pathlib
import cv2
import torch

def parse_boxes(results):
    return [[int(b[0]), int(b[1]), int(b[2]), int(b[3])] for *b, _, _ in results.xyxy[0]]


def detect_products_and_classify(img_path, object_model, sku_model, sku_names):
    """
    Two-stage detection with priority for SKU model:
    1. Get all SKU detections first - trust these classifications
    2. Add object detections only for objects not covered by SKU detections
    """
    # First get all SKU model detections - we trust these classifications
    img_bgr = cv2.imread(img_path)
    img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
    sku_results = sku_model(img_rgb)
    sku_boxes = parse_boxes(sku_results)
    sku_classes = [int(cls) for *_, _, cls in sku_results.xyxy[0]]
    sku_classes_conf = [conf for *_, conf, _ in sku_results.xyxy[0]]
    
    # Create a list of all classified products from SKU model
    classified_products = []
    for i, sku_box in enumerate(sku_boxes):
        label = sku_names[sku_classes[i]]
        conf = sku_classes_conf[i]
        classified_products.append((sku_box, label,conf))
    
    # Now detect all product objects
    object_results = object_model(img_rgb)
    all_product_boxes = parse_boxes(object_results)
    
    classified_objects = []
    for i, obj_box in enumerate(all_product_boxes):
        classified_objects.append((obj_box, 'object'))
    
    return classified_products,classified_objects

def load_model(model_path):
    # For Windows compatibility -- remove this line if running on Linux
    # temp = pathlib.PosixPath
    # pathlib.PosixPath = pathlib.WindowsPath
    model = torch.hub.load('yolov5', 'custom', path=model_path,source='local', force_reload=True)
    return model