# from pathlib import Path
# import pathlib
import cv2
import torch
import numpy as np
import logging
from typing import List, Tuple, Optional
from .model_cache import load_model_cached

logger = logging.getLogger(__name__)

def parse_boxes(results) -> List[List[int]]:
    """Extract bounding boxes from YOLO results."""
    if len(results.xyxy[0]) == 0:
        return []
    return [[int(b[0]), int(b[1]), int(b[2]), int(b[3])] for *b, _, _ in results.xyxy[0]]

def preprocess_image(img_path: str) -> Optional[np.ndarray]:
    """Optimized image preprocessing with error handling."""
    try:
        # Read image with error handling
        img_bgr = cv2.imread(img_path)
        if img_bgr is None:
            logger.error(f"Failed to load image: {img_path}")
            return None

        # Convert to RGB once
        img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
        return img_rgb
    except Exception as e:
        logger.error(f"Error preprocessing image {img_path}: {e}")
        return None

def detect_products_and_classify(img_path: str, object_model, sku_model, sku_names: List[str]) -> Tuple[List, List]:
    """
    Optimized two-stage detection with priority for SKU model:
    1. Get all SKU detections first - trust these classifications
    2. Add object detections only for objects not covered by SKU detections
    """
    # Preprocess image once
    img_rgb = preprocess_image(img_path)
    if img_rgb is None:
        return [], []

    try:
        # Run SKU model inference with no_grad for memory efficiency
        with torch.no_grad():
            sku_results = sku_model(img_rgb)

        sku_boxes = parse_boxes(sku_results)

        # Extract classes and confidences efficiently
        if len(sku_results.xyxy[0]) > 0:
            # Convert to CPU and numpy for faster processing
            detections = sku_results.xyxy[0].cpu().numpy()
            sku_classes = detections[:, 5].astype(int)
            sku_classes_conf = detections[:, 4]
        else:
            sku_classes = []
            sku_classes_conf = []

        # Create classified products with bounds checking
        classified_products = []
        for i, (sku_box, cls, conf) in enumerate(zip(sku_boxes, sku_classes, sku_classes_conf)):
            if 0 <= cls < len(sku_names):
                label = sku_names[cls]
                classified_products.append((sku_box, label, float(conf)))
            else:
                logger.warning(f"Invalid class index {cls} for SKU model")

        # Run object model inference
        with torch.no_grad():
            object_results = object_model(img_rgb)

        all_product_boxes = parse_boxes(object_results)
        classified_objects = [(obj_box, 'object') for obj_box in all_product_boxes]

        return classified_products, classified_objects

    except Exception as e:
        logger.error(f"Error during inference: {e}")
        return [], []

async def load_model(model_path: str):
    """Load model with caching and optimizations."""
    return await load_model_cached(model_path)

# Batch processing function for multiple images
async def batch_detect_products(image_paths: List[str], object_model, sku_model, sku_names: List[str]) -> List[Tuple[List, List]]:
    """
    Process multiple images in batch for better throughput.
    """
    results = []

    # Process images in batches to manage memory
    batch_size = 4  # Adjust based on available memory

    for i in range(0, len(image_paths), batch_size):
        batch_paths = image_paths[i:i + batch_size]
        batch_results = []

        for img_path in batch_paths:
            result = detect_products_and_classify(img_path, object_model, sku_model, sku_names)
            batch_results.append(result)

        results.extend(batch_results)

        # Optional: Clear cache between batches if memory is tight
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    return results