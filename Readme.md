Setting up a virtual env

python -m venv venv
.\venv\Scripts\activate
pip install -r requirements.txt
set AZURE_STORAGE_CONNECTION_STRING="<your-connection-string>"
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

Above steps will run the code , but if you want to enable debugging i.e hitting the break points you need to add a launch.json file and configure in the Run and debug console of the visual studio code editor

launch.json file 👇
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: FastAPI",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "main:app",  // Adjust if your FastAPI app is defined in a different file
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}

Install yolov5 model locally to run the project as the code expects the model to be present in local machine
git clone https://github.com/ultralytics/yolov5.git

Docker run bash command
docker build -t myimage .
docker run -p 8000:8000 -it --rm -- myimage /bin/bash (port binding is necessary for request forwarding)

