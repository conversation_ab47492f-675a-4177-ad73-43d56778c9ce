from collections import defaultdict
from typing import Counter
import cv2
import hashlib

def get_unique_color(label):
    # Generate a color from label hash
    hash_bytes = hashlib.md5(label.encode()).digest()
    r, g, b = hash_bytes[0], hash_bytes[1], hash_bytes[2]
    # Avoid red (255,0,0) and black (0,0,0)
    if (r, g, b) in [(255, 0, 0), (0, 0, 0)]:
        r = (r + 100) % 256
        g = (g + 50) % 256
        b = (b + 150) % 256
    return (int(b), int(g), int(r))  # OpenCV uses BGR

def annotate_image(img_path, sku_dets,obj_dets,product_dict):
    img = cv2.imread(img_path)
    font = cv2.FONT_HERSHEY_DUPLEX
    font_scale = 0.5
    thickness = 1

    sku_img = img.copy()
    label_counter = Counter()
    label_to_ids = defaultdict(list)

    for bbox, label, conf in sku_dets:
        x1, y1, x2, y2 = bbox
        if label == "Unknown":
            color = (0, 0, 255)
        else:
            color = get_unique_color(label)

        # Draw the bounding box
        cv2.rectangle(sku_img, (x1, y1), (x2, y2), color, 2)

        # Format text with confidence rounded to 3 decimals
        conf_text = f"{label}_{conf:.3f}"

        # Calculate text size
        (text_w, text_h), _ = cv2.getTextSize(conf_text, font, font_scale, thickness)

        # Position text just below the bounding box
        text_x = x1+2
        text_y = y2 + text_h + 5  # Add a small margin below the box
        #Optional: Draw background for better visibility
        cv2.rectangle(sku_img, (text_x, text_y - text_h - 2), (text_x + text_w, text_y + 2), color, -1)

        # Draw the label text
        cv2.putText(sku_img, conf_text, (text_x, text_y), font, font_scale, (255,255,255), thickness)

        label_counter[label] += 1
        for pid, pname in product_dict.items():
            if pname == label:
                label_to_ids[label].append(pid)

    sku_detection_result = []
    for label, count in label_counter.items():
        product_id = label_to_ids[label][0] if label_to_ids[label] else None
        sku_detection_result.append({
            "Product": label,
            "Label": label,
            "Id": str(product_id),
            "Count": count
        })

    obj_detection_result = []
    total_object_count = 0
     # 2️⃣ Object Image
    obj_image = img.copy()
    for bbox, label in obj_dets:
        x1, y1, x2, y2 = bbox
        color = (0, 255, 0)  # Red for object detect
        cv2.rectangle(obj_image, (x1, y1), (x2, y2), color, 2)
        cv2.putText(obj_image, label, (x1, y2 + 15), font, font_scale, color, thickness)
        total_object_count += 1
    obj_detection_result.append({
            "Product": 'object',
            "Label": 'object',
            "Id": 'object',
            "Count": total_object_count
        })
    return obj_image,sku_img,sku_detection_result,obj_detection_result

# Duplicate function removed - using the one defined at the top